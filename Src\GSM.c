#include "GSM.h"
#include "gpio.h"
#include "globals.h"
#include "cmsis_os.h"  // 添加FreeRTOS支持
#include "network_command.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

// 网络指令相关已在network_command.h中定义

// 内部变量
static char gsm_response_buffer[GSM_RESPONSE_BUFFER_SIZE];
static uint8_t gsm_module_ready = 0;
static uint8_t gsm_connected = 0;
static char last_server_command[32] = {0};  // 存储最后一次服务器ZL指令

// STM32 HAL库串口接口实现
void gsm_uart_send_string(const char* str)
{
    HAL_UART_Transmit(&hlpuart1, (uint8_t*)str, strlen(str), 1000);
}

void gsm_uart_send_byte(uint8_t byte)
{
    HAL_UART_Transmit(&hlpuart1, &byte, 1, 100);
}

uint8_t gsm_uart_receive_byte(void)
{
    uint8_t byte;
    // 使用更长的超时时间进行阻塞接收
    if(HAL_UART_Receive(&hlpuart1, &byte, 1, 10) == HAL_OK)
    {
        return byte;
    }
    return 0;
}

uint8_t gsm_uart_data_available(void)
{
    // 直接使用HAL库的接收状态检查
    return (hlpuart1.RxXferCount < hlpuart1.RxXferSize);
}

/**
 * @brief GSM模块初始化
 * @return gsm_status_t 初始化状态
 */
gsm_status_t gsm_init(void)
{
    gsm_status_t status;
    uint8_t retry_count = 0;

    // 等待模块启动完成
    gsm_delay_ms(1000);

    // 测试通信
    gsm_send_at_command("AT", gsm_response_buffer, 200);

    // 关闭回显
    while(retry_count < GSM_MAX_RETRY_COUNT)
    {
        status = gsm_send_at_command("ATE0", gsm_response_buffer, 200);
        if(status == GSM_OK)
        {
            gsm_module_ready = 1;
            return GSM_OK;
        }
        retry_count++;
        if(retry_count < GSM_MAX_RETRY_COUNT)
        {
            gsm_delay_ms(500);
        }
    }

    // ATE0失败
    return GSM_ERROR;
}

/**
 * @brief 获取模组型号
 * @param model 存储模组型号的缓冲区
 * @return gsm_status_t 执行状态
 */
gsm_status_t gsm_get_model(char* model)
{
    gsm_status_t status;
    char* start_ptr;
    char* end_ptr;

    if(!gsm_module_ready || model == NULL)
        return GSM_ERROR;

    status = gsm_send_at_command("AT+CGMM", gsm_response_buffer, 200);
    if(status != GSM_OK)
        return status;

    // 解析响应: +CGMM: "Air780EG"
    start_ptr = strstr(gsm_response_buffer, "\"");
    if(start_ptr != NULL)
    {
        start_ptr++; // 跳过第一个引号
        end_ptr = strstr(start_ptr, "\"");
        if(end_ptr != NULL)
        {
            *end_ptr = '\0';
            strcpy(model, start_ptr);
            return GSM_OK;
        }
    }

    return GSM_ERROR;
}

/**
 * @brief 获取CCID号
 * @param ccid 存储CCID号的缓冲区
 * @return gsm_status_t 执行状态
 */
gsm_status_t gsm_get_ccid(char* ccid)
{
    gsm_status_t status;
    char* start_ptr;
    char* end_ptr;

    if(!gsm_module_ready || ccid == NULL)
        return GSM_ERROR;

    status = gsm_send_at_command("AT+CCID", gsm_response_buffer, 200);
    if(status != GSM_OK)
        return status;

    // 解析响应，查找数字开头的CCID
    start_ptr = gsm_response_buffer;
    while(*start_ptr != '\0')
    {
        if(*start_ptr >= '0' && *start_ptr <= '9')
        {
            end_ptr = start_ptr;
            while(*end_ptr >= '0' && *end_ptr <= '9')
                end_ptr++;

            if(end_ptr - start_ptr >= 19) // CCID一般19-20位
            {
                *end_ptr = '\0';
                strcpy(ccid, start_ptr);
                return GSM_OK;
            }
        }
        start_ptr++;
    }

    return GSM_ERROR;
}

/**
 * @brief 获取电压
 * @param voltage 存储电压值的指针(mV)
 * @return gsm_status_t 执行状态
 */
gsm_status_t gsm_get_voltage(uint16_t* voltage)
{
    gsm_status_t status;
    char* start_ptr;

    if(!gsm_module_ready || voltage == NULL)
        return GSM_ERROR;

    status = gsm_send_at_command("AT+CBC", gsm_response_buffer, 200);
    if(status != GSM_OK)
        return status;

    // 解析响应: +CBC: 3489
    start_ptr = strstr(gsm_response_buffer, "+CBC: ");
    if(start_ptr != NULL)
    {
        start_ptr += 6; // 跳过"+CBC: "
        *voltage = (uint16_t)atoi(start_ptr);
        return GSM_OK;
    }

    return GSM_ERROR;
}

/**
 * @brief 获取信号强度
 * @param rssi 信号强度指针
 * @param ber 误码率指针
 * @return gsm_status_t 执行状态
 */
gsm_status_t gsm_get_signal(uint8_t* rssi, uint8_t* ber)
{
    gsm_status_t status;
    char* start_ptr;
    char* comma_ptr;

    if(!gsm_module_ready || rssi == NULL || ber == NULL)
        return GSM_ERROR;

    status = gsm_send_at_command("AT+CSQ", gsm_response_buffer, 200);
    if(status != GSM_OK)
        return status;

    // 解析响应: +CSQ: 17,0
    start_ptr = strstr(gsm_response_buffer, "+CSQ: ");
    if(start_ptr != NULL)
    {
        start_ptr += 6; // 跳过"+CSQ: "
        comma_ptr = strstr(start_ptr, ",");
        if(comma_ptr != NULL)
        {
            *comma_ptr = '\0';
            *rssi = (uint8_t)atoi(start_ptr);
            *ber = (uint8_t)atoi(comma_ptr + 1);
            return GSM_OK;
        }
    }

    return GSM_ERROR;
}

/**
 * @brief 连接到默认服务器
 * @return gsm_status_t 连接状态
 */
gsm_status_t gsm_connect_default_server(void)
{
    return gsm_tcp_connect(GSM_SERVER_IP, GSM_SERVER_PORT);
}

/**
 * @brief 发送数据（自动计算字节数，等待ZL指令回复）
 * @param data_string 要发送的数据字符串
 * @param server_command 存储服务器ZL指令的缓冲区（可以为NULL）
 * @return gsm_status_t 发送状态
 */
gsm_status_t gsm_send_data(const char* data_string, char* server_command)
{
    char cmd_buffer[32];
    uint16_t data_len;
    char* zl_ptr;

    if(!gsm_module_ready || !gsm_connected || data_string == NULL)
        return GSM_ERROR;

    // 自动计算数据长度
    data_len = strlen(data_string);

    // 发送数据长度命令
    snprintf(cmd_buffer, sizeof(cmd_buffer), "AT+CIPSEND=%d", data_len);
    printf("GSM send: %s\r\n", cmd_buffer);
    gsm_send_at_command(cmd_buffer, gsm_response_buffer, 200);
    osDelay(100);  // 等待100ms，不检测回复

    // 发送实际数据
    gsm_uart_send_string(data_string);

    // 持续接收300ms，检测ZL指令
    status = gsm_wait_for_response(gsm_response_buffer, 300);
    // 查找ZL指令：ZL+H4+F0E0+N0
    zl_ptr = strstr(gsm_response_buffer, "ZL+");
    if(zl_ptr != NULL)
        {
            // 提取ZL指令到内部缓冲区
            char* end_ptr = zl_ptr;
            while(*end_ptr != '\0' && *end_ptr != '\r' && *end_ptr != '\n')
                end_ptr++;

            uint16_t cmd_len = end_ptr - zl_ptr;
            if(cmd_len < sizeof(last_server_command))
            {
                strncpy(last_server_command, zl_ptr, cmd_len);
                last_server_command[cmd_len] = '\0';

                // 如果用户提供了缓冲区，也复制给用户
                if(server_command != NULL)
                {
                    strcpy(server_command, last_server_command);
                }

                printf("ZL command received: %s\r\n", last_server_command);

                // 解析ZL指令并应用配置
                NetworkCommand_Result_t zl_result;
                NetworkCommand_Parse(last_server_command, &zl_result);

                return GSM_OK;  // 发送成功且收到ZL指令
            }
        }
    else
    {
        return GSM_ERROR;  // 未收到ZL指令视为发送失败
    }

    // 这里不应该到达，上面已经处理了所有情况
    return GSM_ERROR;
}

/**
 * @brief 获取最后一次服务器ZL指令
 * @param command_buffer 存储指令的缓冲区
 * @return gsm_status_t 获取状态
 */
gsm_status_t gsm_get_last_server_command(char* command_buffer)
{
    if(command_buffer == NULL)
        return GSM_ERROR;

    if(strlen(last_server_command) > 0)
    {
        strcpy(command_buffer, last_server_command);
        return GSM_OK;
    }

    return GSM_ERROR;
}

/**
 * @brief TCP连接到服务器
 * @param ip 服务器IP地址
 * @param port 服务器端口
 * @return gsm_status_t 连接状态
 */
gsm_status_t gsm_tcp_connect(const char* ip, uint16_t port)
{
    char cmd_buffer[64];

    if(!gsm_module_ready || ip == NULL)
        return GSM_ERROR;

    // TCP连接
    snprintf(cmd_buffer, sizeof(cmd_buffer), "AT+CIPSTART=\"TCP\",\"%s\",%d", ip, port);
    gsm_send_at_command(cmd_buffer, gsm_response_buffer, 200);
    osDelay(100);  // 等待100ms，不检测回复

    // 设置快发模式
    gsm_send_at_command("AT+CIPQSEND=1", gsm_response_buffer, 200);
    osDelay(100);  // 等待100ms，不检测回复

    gsm_connected = 1;
    return GSM_OK;
}

/**
 * @brief 发送TCP数据
 * @param data 要发送的数据
 * @param len 数据长度
 * @return gsm_status_t 发送状态
 */
gsm_status_t gsm_tcp_send(const char* data, uint16_t len)
{
    gsm_status_t status;
    char cmd_buffer[32];

    if(!gsm_module_ready || !gsm_connected || data == NULL || len == 0)
        return GSM_ERROR;

    // 如果len为0，自动计算数据长度
    if(len == 0)
        len = gsm_calculate_data_length(data);

    // 发送数据长度命令
    snprintf(cmd_buffer, sizeof(cmd_buffer), "AT+CIPSEND=%d", len);
    status = gsm_send_at_command(cmd_buffer, gsm_response_buffer, GSM_DEFAULT_TIMEOUT);
    if(status != GSM_OK)
        return status;

    // 等待'>'提示符
    if(strstr(gsm_response_buffer, ">") == NULL)
        return GSM_ERROR;

    // 发送实际数据
    gsm_uart_send_string(data);

    // 等待发送确认
    status = gsm_wait_for_response(gsm_response_buffer, GSM_SEND_TIMEOUT);
    if(status == GSM_OK && strstr(gsm_response_buffer, "DATA ACCEPT") != NULL)
    {
        return GSM_OK;
    }

    return GSM_ERROR;
}

/**
 * @brief 关闭TCP连接
 * @return gsm_status_t 关闭状态
 */
gsm_status_t gsm_tcp_close(void)
{
    if(!gsm_module_ready)
        return GSM_ERROR;

    gsm_send_at_command("AT+CIPCLOSE", gsm_response_buffer, 200);
    osDelay(100);  // 等待100ms，不检测回复

    gsm_connected = 0;
    return GSM_OK;
}

/**
 * @brief 获取GSM模块完整信息
 * @param info 存储信息的结构体指针
 * @return gsm_status_t 执行状态
 */
gsm_status_t gsm_get_info(gsm_info_t* info)
{
    if(!gsm_module_ready || info == NULL)
        return GSM_ERROR;

    // 清空结构体
    memset(info, 0, sizeof(gsm_info_t));

    // 获取各项信息
    gsm_get_model(info->model);
    gsm_get_ccid(info->ccid);
    gsm_get_voltage(&info->voltage);
    gsm_get_signal(&info->rssi, &info->ber);

    return GSM_OK;
}

/**
 * @brief 发送AT指令并等待响应
 * @param cmd AT指令
 * @param response 响应缓冲区
 * @param timeout 超时时间(ms)
 * @return gsm_status_t 执行状态
 */
gsm_status_t gsm_send_at_command(const char* cmd, char* response, uint32_t timeout)
{
    if(cmd == NULL || response == NULL)
        return GSM_ERROR;

    // 确保缓存是空的
    memset(response, 0, GSM_RESPONSE_BUFFER_SIZE);

    // 发送AT指令
    printf("TX: %s\r\n", cmd);
    gsm_uart_send_string(cmd);
    gsm_uart_send_string("\r\n");

    // 立即开始接收，使用传入的超时参数
    gsm_status_t status = gsm_wait_for_response(response, timeout);

    return status;
}

/**
 * @brief 等待GSM模块响应
 * @param response 响应缓冲区
 * @param timeout 超时时间(ms)
 * @return gsm_status_t 响应状态
 */
gsm_status_t gsm_wait_for_response(char* response, uint32_t timeout)
{
    if(response == NULL)
        return GSM_ERROR;

    // 清空响应缓冲区
    memset(response, 0, GSM_RESPONSE_BUFFER_SIZE);

    uint32_t start_time = HAL_GetTick();
    uint16_t total_received = 0;
    // 简化打印，不显示时间

    // 直接接收最大数据量，让串口自动等待所有数据，超时后自动返回
    // 根据超时参数接收，连接前AT指令用200ms，数据发送用2000ms
    while((HAL_GetTick() - start_time) < timeout)
    {
        uint8_t byte;
        // 逐字节接收，超时1ms
        HAL_StatusTypeDef uart_status = HAL_UART_Receive(&hlpuart1, &byte, 1, 1);

        if(uart_status == HAL_OK)
        {
            response[total_received] = byte;
            total_received++;
            response[total_received] = '\0';  // 保持字符串结束符
        }
    }

    // 打印实际接收的内容
    printf("RX: %s\r\n", response);

    // 总是等待完整2秒，然后返回成功（让上层判断数据内容）
    return GSM_OK;
}

/**
 * @brief 延时函数
 * @param ms 延时时间(毫秒)
 */
void gsm_delay_ms(uint32_t ms)
{
    osDelay(ms);  // 使用FreeRTOS延时，避免阻塞其他任务
}

/**
 * @brief 计算数据长度
 * @param data 数据字符串
 * @return uint16_t 数据长度
 */
uint16_t gsm_calculate_data_length(const char* data)
{
    if(data == NULL)
        return 0;

    return strlen(data);
}

/**
 * @brief GSM电源开启
 */
void gsm_power_on(void)
{
    // 检查当前引脚状态
    GPIO_PinState current_state = HAL_GPIO_ReadPin(RF_PWR_GPIO_Port, RF_PWR_Pin);
    printf("GSM power: RF_PWR current state: %s\r\n", current_state ? "HIGH" : "LOW");

    printf("GSM power: setting RF_PWR pin HIGH (GPIO_PIN_SET)\r\n");
    HAL_GPIO_WritePin(RF_PWR_GPIO_Port, RF_PWR_Pin, GPIO_PIN_SET);

    // 再次检查引脚状态
    current_state = HAL_GPIO_ReadPin(RF_PWR_GPIO_Port, RF_PWR_Pin);
    printf("GSM power: RF_PWR after set: %s\r\n", current_state ? "HIGH" : "LOW");

    printf("GSM power: waiting 8s for module startup...\r\n");
    gsm_delay_ms(8000);  // 增加等待时间，CAT1模块启动较慢
    printf("GSM power: power on complete\r\n");
}

/**
 * @brief GSM电源关闭
 */
void gsm_power_off(void)
{
    HAL_GPIO_WritePin(RF_PWR_GPIO_Port, RF_PWR_Pin, GPIO_PIN_RESET);
}

/**
 * @brief GSM紧急关机
 */
void gsm_emergency_shutdown(void)
{
    // 发送紧急关机指令
    gsm_uart_send_string("AT+CPOWD=0\r\n");
    gsm_delay_ms(1000);  // 等待关机完成
    gsm_power_off();     // 断电
}

/**
 * @brief GSM简化初始化流程（兼容原有接口）
 * @return gsm_status_t 初始化状态
 */
gsm_status_t gsm_simple_init(void)
{
    gsm_status_t status;

    // 开启GSM电源
    gsm_power_on();

    // 初始化GSM模块
    status = gsm_init();
    if(status != GSM_OK)
    {
        return status;
    }

    // 获取并保存CCID到全局变量
    gsm_get_ccid(gsm_ccid);

    // 获取并保存信号强度到全局变量
    uint8_t rssi, ber;
    if(gsm_get_signal(&rssi, &ber) == GSM_OK)
    {
        gsm_signal_quality = (int8_t)rssi;
    }

    // 连接到默认服务器
    status = gsm_connect_default_server();
    if(status != GSM_OK)
    {
        return status;
    }

    return GSM_OK;
}

// ============================================================================
// 兼容性函数实现 - 保持原有接口不变
// ============================================================================

/**
 * @brief GSM模块初始化（兼容原有接口）
 * @return GSM_Status_t 初始化状态
 */
GSM_Status_t GSM_Init(void)
{
    return (GSM_Status_t)gsm_init();
}

/**
 * @brief GSM简化初始化流程（兼容原有接口）
 * @return GSM_Status_t 初始化状态
 */
GSM_Status_t GSM_SimpleInit(void)
{
    return (GSM_Status_t)gsm_simple_init();
}

/**
 * @brief GSM完整初始化流程（兼容原有接口）
 * @return GSM_Status_t 初始化状态
 */
GSM_Status_t GSM_FullInit(void)
{
    return (GSM_Status_t)gsm_simple_init();
}

/**
 * @brief 获取CCID号（兼容原有接口）
 * @param ccid CCID字符串缓冲区
 * @return GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetCCID(char* ccid)
{
    return (GSM_Status_t)gsm_get_ccid(ccid);
}

/**
 * @brief 获取信号强度（兼容原有接口）
 * @param signal 信号强度指针(0-31)
 * @return GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetSignal(uint8_t* signal)
{
    uint8_t ber;
    gsm_status_t status = gsm_get_signal(signal, &ber);

    // 同时更新全局变量
    if(status == GSM_OK)
    {
        gsm_signal_quality = (int8_t)*signal;
    }

    return (GSM_Status_t)status;
}

/**
 * @brief 发送数据（兼容原有接口）
 * @param data 要发送的数据
 * @param length 数据长度
 * @return GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SendData(const char* data, uint16_t length)
{
    char server_command[64];
    return (GSM_Status_t)gsm_send_data(data, server_command);
}

/**
 * @brief 获取模块信息（兼容原有接口）
 * @param info 模块信息结构体指针
 * @return GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetInfo(GSM_Info_t* info)
{
    if(info == NULL)
        return GSM_ERROR;

    gsm_info_t gsm_info;
    gsm_status_t status = gsm_get_info(&gsm_info);

    if(status == GSM_OK)
    {
        strcpy(info->model, gsm_info.model);
        strcpy(info->ccid, gsm_info.ccid);
        info->voltage = gsm_info.voltage;
        info->signal = gsm_info.rssi;
        info->network_reg = 1; // 假设已注册
    }

    return (GSM_Status_t)status;
}

/**
 * @brief 获取当前状态（兼容原有接口）
 * @return GSM_State_t 当前状态
 */
GSM_State_t GSM_GetState(void)
{
    if(gsm_module_ready && gsm_connected)
        return GSM_STATE_CONNECTED;
    else if(gsm_module_ready)
        return GSM_STATE_READY;
    else
        return GSM_STATE_INIT;
}

/**
 * @brief GSM电源开启（兼容原有接口）
 */
void GSM_PowerOn(void)
{
    gsm_power_on();
}

/**
 * @brief GSM电源关闭（兼容原有接口）
 */
void GSM_PowerOff(void)
{
    gsm_power_off();
}

/**
 * @brief GSM紧急关机（兼容原有接口）
 */
void GSM_EmergencyShutdown(void)
{
    gsm_emergency_shutdown();
}

/**
 * @brief 关闭服务器连接（兼容原有接口）
 * @return GSM_Status_t 关闭状态
 */
GSM_Status_t GSM_CloseServer(void)
{
    return (GSM_Status_t)gsm_tcp_close();
}
